import { TestBed } from '@angular/core/testing';
import { HawbSearchRequestService } from './hawb-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../../sli-mgmt/ref-data/airports.data';
import { HAWB_LIST } from '../ref-data/hawb-list.data';
import { HawbListObject } from '../models/hawb-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { HawbSearchPayload } from '../models/hawb-search-payload.model';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('HawbSearchRequestService', () => {
	let service: HawbSearchRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [HawbSearchRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(HawbSearchRequestService);
	});

	describe('getOptions', () => {
		it('should retrieve shipper organizations when id is "shipper"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'shipper';

			const mockApiResponse = ['Demo Shipper', 'Demo Shipper 2'];
			httpClientSpy.get.and.returnValue(of(mockApiResponse));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Demo Shipper');
					expect(response[0].name).toBe('Demo Shipper');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/get-keyword`, {
				params: jasmine.any(Object),
			});
		});
	});

	describe('getHawbList', () => {
		it('should retrieve all SLI list items', (done: DoneFn) => {
			// Arrange
			const hawbSearchPayload: HawbSearchPayload = {};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<HawbListObject> = {
				rows: HAWB_LIST,
				total: HAWB_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getHawbList(paginationRequest, hawbSearchPayload).subscribe({
				next: (response: PaginationResponse<HawbListObject>) => {
					// Assert
					expect(response.rows.length).toBe(HAWB_LIST.length);
					expect(response.total).toBe(HAWB_LIST.length);
					expect(response.rows[0].hawbNumber).toBe(HAWB_LIST[0].hawbNumber);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const hawbSearchPayload: HawbSearchPayload = {
				hawbNumberList: ['H000920'],
				departureLocationList: ['DEP A'],
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<HawbListObject> = {
				rows: [HAWB_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getHawbList(paginationRequest, hawbSearchPayload).subscribe({
				next: (response: PaginationResponse<HawbListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].hawbNumber).toBe('H000920');
					expect(response.rows[0].origin).toBe('DEP A');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { params: jasmine.any(Object) });
		});
	});
});
