// eslint-disable-next-line
export const environment = {
	production: false,
	faqUrl: 'https://orll-test.iata-asd.com',
	supportUrl: 'https://orll-test.iata-asd.com',

	msalClientId: 'c916a67e-3f7b-4d65-994b-fa9ec0255bf7',
	msalAuthority: 'https://iataazurenonprodb2c.b2clogin.com/iataazurenonprodb2c.onmicrosoft.com/B2C_1A_signup_signin',
	msalScopeBaseUrl: 'https://iataazurenonprodb2c.onmicrosoft.com/orll-staging',
	msalAfterLoginUrl: 'https://orll-test.iata-asd.com',
	msalAfterLogoutUrl: 'https://iata--pprod.sandbox.my.site.com/csportal/s/login/?language=en_US',

	msalEnabled: false,
	backendEnabled: true,
	baseApi: 'https://orll-test.iata-asd.com/api',
	mfBaseUrl: 'http://localhost:4200/assets',

	userId: 'shipper1',
	orgId: '0b62001e-3b06-4d97-bc8b-182bd6efa613',

	userList: [
		{
			userId: 'shipper1',
			orgId: '0b62001e-3b06-4d97-bc8b-182bd6efa613',
		},
		{
			userId: 'forwarder1',
			orgId: '486c161d-23d2-468a-8234-26b31107a03f',
		},
	],
};
