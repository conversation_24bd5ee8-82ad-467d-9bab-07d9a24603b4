import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, QueryList, ViewChildren } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router } from '@angular/router';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ShipmentParty } from '../../models/shipment-party.model';
import { SliShipperComponent } from '../../components/sli-shipper/sli-shipper.component';
import { SliConsigneeComponent } from '../../components/sli-consignee/sli-consignee.component';
import { SliRoutingComponent } from '../../components/sli-routing/sli-routing.component';
import { SliPieceListComponent } from '../../components/sli-piece-list/sli-piece-list.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SliCreatePayload } from '../../models/sli-create-payload.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatDialog } from '@angular/material/dialog';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { map, Observable } from 'rxjs';
import { CanComponentDeactivate } from '@shared/services/can-deactivate.guard';
import { Person } from '@shared/models/person.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { environment } from '@environments/environment';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { AsyncPipe } from '@angular/common';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { Modules, UserPermission } from '@shared/models/user-role.model';

@Component({
	selector: 'orll-sli-create-page',
	templateUrl: './sli-create-page.component.html',
	styleUrls: ['./sli-create-page.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		TranslateModule,
		SliShipperComponent,
		SliConsigneeComponent,
		SliRoutingComponent,
		SliPieceListComponent,
		SpinnerComponent,
		AsyncPipe,
	],
})
export default class SliCreatePageComponent extends RolesAwareComponent implements OnInit, CanComponentDeactivate {
	@ViewChildren(SliShipperComponent) sliShipper!: QueryList<SliShipperComponent>;
	@ViewChildren('sliConsignee') sliConsignee!: QueryList<SliConsigneeComponent>;
	@ViewChildren(SliRoutingComponent) sliRouting!: QueryList<SliRoutingComponent>;
	@ViewChildren(SliPieceListComponent) sliPieceList!: QueryList<SliPieceListComponent>;

	@Input() sliNumber = '';

	shipperInfo: OrgInfo | ShipmentParty | null = null;
	consigneeInfo: ShipmentParty | null = null;
	alsoNotifies: ShipmentParty[] = [];
	dataLoading = false;
	isSaved = false;
	isConfirmed = false;
	isPiece = false;
	pieceType = '';

	readonly SLI_MODULE = Modules.SLI;
	readonly PERMISSION_SAVE = [UserPermission.CREATE, UserPermission.UPDATE].join(',');

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute,
		private readonly cdr: ChangeDetectorRef,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly translate: TranslateService,
		private readonly dialog: MatDialog
	) {
		super();
	}

	ngOnInit(): void {
		if (!this.sliNumber) {
			this.fillShipperInfo();
		} else {
			this.getSliDetail(this.sliNumber);
		}
	}

	getSliDetail(sliNumber: string): void {
		this.sliCreateRequestService
			.getSliDetail(sliNumber)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					const data = res;
					this.shipperInfo = data.shipmentParty.find((item) => item.companyType === OrgType.SHIPPER) ?? null;
					this.consigneeInfo = data.shipmentParty.find((item) => item.companyType === OrgType.CONSIGNEE) ?? null;
					this.alsoNotifies = data.shipmentParty.filter((item) => !item.companyType) ?? [];
					this.sliRouting.first?.sliRoutingForm.patchValue({
						departureLocation: data.departureLocation,
						arrivalLocation: data.arrivalLocation,
						shippingInfo: data.shippingInfo,
					});
					this.sliPieceList.first?.sliPieceListForm.patchValue({
						goodsDescription: data.goodsDescription,
						totalGrossWeight: data.totalGrossWeight,
						dimLength: data.totalDimensions.length,
						dimWidth: data.totalDimensions.width,
						dimHeight: data.totalDimensions.height,
						declaredValueForCustoms: data.declaredValueForCustoms?.numericalValue ?? 'NCV',
						declaredValueForCarriage: data.declaredValueForCarriage?.numericalValue ?? 'NVD',
						insuredAmount: data.insuredAmount?.numericalValue ?? 'NIL',
						declaredValueForCustomsCurrency: data.declaredValueForCustoms?.currencyUnit ?? '',
						declaredValueForCarriageCurrency: data.declaredValueForCarriage?.currencyUnit ?? '',
						insuredAmountCurrency: data.insuredAmount?.currencyUnit ?? '',
						textualHandlingInstructions: data.textualHandlingInstructions,
						weightValuationIndicator: data.weightValuationIndicator,
						incoterms: data.incoterms,
					});
					this.cdr.markForCheck();
				},
			});
	}

	fillShipperInfo(): void {
		this.orgMgmtRequestService
			.getOrgInfo(environment.orgId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.shipperInfo = res;
					this.cdr.markForCheck();
				},
			});
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			textualPostCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					return {
						...item,
						companyName: result.companyName,
						contactName:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						textualPostCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
						emailAddress:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
						companyType: '',
					};
				}
				return item;
			});

			this.cdr.markForCheck();
		});
	}

	private sliDetailRequest(sliCreatePayload: SliCreatePayload): Observable<string | null> {
		if (!this.sliNumber) {
			return this.sliCreateRequestService.createSli(sliCreatePayload);
		} else {
			return this.sliCreateRequestService.updateSli(sliCreatePayload, this.sliNumber);
		}
	}

	onSave(pieceParams?: { pieceType: string; pieceId?: string }): void {
		this.sliShipper.forEach((comp) => comp.sliShipperForm?.markAllAsTouched());
		this.sliRouting.forEach((comp) => comp.sliRoutingForm?.markAllAsTouched());
		this.sliPieceList.forEach((comp) => comp.sliPieceListForm?.markAllAsTouched());

		const shipperData = this.sliShipper.first?.getFormData();
		const consigneeData = this.sliConsignee.first?.getFormData();
		const routingData = this.sliRouting.first?.getFormData();
		const pieceListData = this.sliPieceList.first?.getFormData();

		if (!shipperData || !consigneeData || !routingData || !pieceListData) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		shipperData.companyType = OrgType.SHIPPER;
		const sliCreatePayload = {
			shipmentParty: [shipperData, consigneeData, ...this.alsoNotifies],
			...routingData,
			...pieceListData,
		} as SliCreatePayload;

		this.dataLoading = true;

		this.sliDetailRequest(sliCreatePayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();

					const sliNumber = res;
					let routePath = [];
					if (pieceParams) {
						if (pieceParams.pieceId) {
							routePath = ['piece', pieceParams.pieceType, pieceParams.pieceId];
						} else {
							routePath = ['piece', pieceParams.pieceType, this.sliNumber ?? sliNumber];
						}
					} else {
						routePath = ['sli'];
					}
					this.router.navigate(routePath, { relativeTo: this.route });
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	onCancel(): void {
		if (this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			dialogRef.afterClosed().subscribe((confirmed) => {
				if (confirmed) {
					this.isConfirmed = true;
					this.router.navigate(['/sli'], { relativeTo: this.route });
				}
			});
		} else {
			this.router.navigate(['/sli'], { relativeTo: this.route });
		}
	}

	canDeactivate(): boolean | Observable<boolean> {
		if (this.isSaved) return true;

		if (!this.isConfirmed && this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			return dialogRef.afterClosed().pipe(map((confirmed) => !!confirmed));
		} else {
			return true;
		}
	}

	hasUnsavedChanges(): boolean {
		const shipperData = this.sliShipper.first?.getFormData(true);
		const hasShipperData = shipperData ? this.hasRealData(shipperData) : false;

		const consigneeData = this.sliConsignee.first?.getFormData();
		const hasConsigneeData = consigneeData ? this.hasRealData(consigneeData) : false;

		const routingData = this.sliRouting.first?.getFormData(true);
		const hasRoutingData = routingData ? this.hasRealData(routingData) : false;

		const pieceListData = this.sliPieceList.first?.getFormData(true);
		const hasPieceListData = pieceListData ? this.hasRealData(pieceListData) : false;

		const pieceListTableData = this.sliPieceList.first?.pieceList;
		const haspieceListTableData = pieceListData ? this.hasRealData(pieceListTableData) : false;

		const hasAlsoNotifies = this.alsoNotifies.some((item) => this.hasRealData(item));

		return hasShipperData || hasConsigneeData || hasAlsoNotifies || hasRoutingData || hasPieceListData || haspieceListTableData;
	}

	hasRealData(obj: Record<string, any>): boolean {
		if (!obj) return false;
		return Object.values(obj).some((value) => {
			if (Array.isArray(value)) return value.length > 0;
			if (typeof value === 'object') return this.hasRealData(value);
			return String(value).trim();
		});
	}
}
