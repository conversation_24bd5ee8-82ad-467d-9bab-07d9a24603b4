import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, Routes } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { environment } from '@environments/environment';
import { UserRole } from '@shared/models/user-role.model';
import { CanDeactivateGuard } from '@shared/services/can-deactivate.guard';
import { RouteGuardService } from '@shared/services/route-guard.service';

const MENU_SLI_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
const MENU_HAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];
const MENU_MAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];

export const ROUTES: Routes = [
	{
		path: 'sli',
		data: {
			breadcrumb: {
				label: 'sli.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
				canActivate: [...(environment.msalEnabled ? [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)] : [])],
			},
			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.create',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
					},
					{
						path: 'piece/:pieceType/:sliNumber',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
						data: {
							breadcrumb: {
								label: 'sli.piece.add',
							},
						},
					},
				],
			},
			{
				path: 'edit/:sliNumber',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.edit',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
					},
					{
						path: 'piece/:pieceType/:pieceId',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
						data: {
							breadcrumb: {
								label: 'sli.piece.edit',
							},
						},
					},
				],
			},
		],
	},

	{
		path: 'hawb',
		data: {
			breadcrumb: {
				label: 'hawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
				canActivate: [
					...(environment.msalEnabled ? [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)] : []),
				],
			},
			{
				path: 'edit/:hawbId',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.edit',
					},
				},
				loadComponent: () => import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
				canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
			},

			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.create.fromSli',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
						data: {
							fromCreateHawb: true,
						},
					},
					{
						path: ':fromSli/detail',
						data: {
							breadcrumb: {
								label: 'hawb.mgmt.create.fromSliDetail',
							},
						},
						loadComponent: () =>
							import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
						resolve: {
							sliNumber: (route: ActivatedRouteSnapshot) => route.paramMap.get('fromSli'),
						},
						canActivate: [...(environment.msalEnabled ? [MsalGuard] : [])],
					},
				],
			},
		],
	},

	{
		path: 'mawb',
		data: {
			breadcrumb: {
				label: 'mawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-list/mawb-list.component'),
				canActivate: [
					...(environment.msalEnabled ? [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)] : []),
				],
			},
		],
	},

	{
		path: 'users-mgmt',
		data: {
			breadcrumb: {
				label: 'users.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/user-mgmt/pages/user-list/user-list-page.component'),
				canActivate: [...(environment.msalEnabled ? [MsalGuard, () => inject(RouteGuardService).isSuperUser()] : [])],
			},
		],
	},

	{ path: '**', redirectTo: 'sli' },
] as Routes;
