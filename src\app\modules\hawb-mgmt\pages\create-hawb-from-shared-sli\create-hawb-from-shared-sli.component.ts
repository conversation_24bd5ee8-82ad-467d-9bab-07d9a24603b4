import { AfterViewInit, Component, Input, OnInit, ViewChild } from '@angular/core';
import { AsyncPipe, DatePipe } from '@angular/common';
import { ShipperOrConsigneeInfoComponent } from './shipper-or-consignee-info/shipper-or-consignee-info.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CarrierAgentComponent } from './carrier-agent/carrier-agent.component';
import { IssuedByComponent } from './issued-by/issued-by.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { AirportInfoComponent } from './airport-info/airport-info.component';
import { MatTableModule } from '@angular/material/table';
import { OtherChargesComponent } from './other-charges/other-charges.component';
import { SliConsigneeComponent } from '../../../sli-mgmt/components/sli-consignee/sli-consignee.component';
import { ShipmentParty } from '../../../sli-mgmt/models/shipment-party.model';
import { PrepaidCollectComponent } from './prepaid-collect/prepaid-collect.component';
import { combineLatest, EMPTY, startWith, tap } from 'rxjs';
import { ShipmentPartyCompanyType } from '../../models/hawb-sli-info.model';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { SliCreatePayload } from '../../../sli-mgmt/models/sli-create-payload.model';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { Person } from '@shared/models/person.model';
import { OrgType } from '@shared/models/org-type.model';
import { MatDialog } from '@angular/material/dialog';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { HawbCreateDto, OtherChargeList, PartyList } from '../../models/hawb-create.model';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule, provideNativeDateAdapter } from '@angular/material/core';
import { EnumCodeFormItemComponent } from '@shared/components/enum-code-form-item/enum-code-form-item.component';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { AuthService } from '@shared/auth/auth.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Router } from '@angular/router';
import { isBlank } from '@shared/utils/type.utils';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { MatSelectChange } from '@angular/material/select';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EmitCharges } from '../../models/other-charges.model';
import { catchError } from 'rxjs/operators';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';

const DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';
const REGX_NUMBER_2_DECIMAL = /^\d+(\.\d{1,2})?$/;

@Component({
	selector: 'orll-create-hawb-from-shared-sli',
	imports: [
		ShipperOrConsigneeInfoComponent,
		MatButtonModule,
		MatExpansionModule,
		MatIconModule,
		TranslateModule,
		SliConsigneeComponent,
		CarrierAgentComponent,
		IssuedByComponent,
		MatFormFieldModule,
		MatInput,
		ReactiveFormsModule,
		AirportInfoComponent,
		MatTableModule,
		OtherChargesComponent,
		PrepaidCollectComponent,
		CurrencyInputComponent,
		MatAutocompleteModule,
		MatOptionModule,
		EnumCodeFormItemComponent,
		MatDatepickerModule,
		AsyncPipe,
	],
	providers: [provideNativeDateAdapter(), DatePipe],
	templateUrl: './create-hawb-from-shared-sli.component.html',
	styleUrl: './create-hawb-from-shared-sli.component.scss',
})
export default class CreateHawbFromSharedSliComponent extends RolesAwareComponent implements OnInit, AfterViewInit {
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	@Input() sliNumber?: string;

	@Input() hawbId?: string;

	@ViewChild(CarrierAgentComponent)
	carrierAgentComponent!: CarrierAgentComponent;

	@ViewChild(AirportInfoComponent)
	airPortInfoComponent!: AirportInfoComponent;

	@ViewChild(OtherChargesComponent)
	otherChargesComponent!: OtherChargesComponent;

	@ViewChild(PrepaidCollectComponent)
	prepaidCollectComponent!: PrepaidCollectComponent;

	private sliId: string | undefined;
	private orgId: string | undefined;

	alsoNotifies: ShipmentParty[] = [];
	isConfirmed = false;

	hawbForm = this.fb.group({
		hawbPrefix: ['', [Validators.required]],
		hawbNumber: ['', [Validators.required]],
		accountingInformation: [null],
		handingInformation: [''],
		noOfPiecesRcp: new FormControl<number | null>({ value: null, disabled: true }),
		grossWeight: new FormControl<number | null>(null, [Validators.required, Validators.pattern('^\\d+(\\.\\d{1})?$')]),
		rateClass: new FormControl<string | null>(null),
		chargeableWeight: new FormControl<number | null>(null, [Validators.required, Validators.pattern(/^\d+(\.5)?$/)]),
		rateCharge: new FormGroup(
			{
				currencyUnit: new FormControl('', Validators.required),
				numericalValue: new FormControl<number | null>(null, [Validators.required, Validators.pattern('^\\d+(\\.\\d{1,2})?$')]),
			},
			[Validators.required]
		),
		total: new FormControl<number | null>({ value: null, disabled: true }),
		natureAndQuantityOfGoods: ['', [Validators.required]],
		//last row
		date: new FormControl<Date | null>(null, [Validators.required]),
		atPlace: ['', [Validators.required]],
		signatureOfShipperOrHisAgent: ['', [Validators.required]],
		signatureOfCarrierOrItsAgent: ['', [Validators.required]],
	});

	displayedColumns: string[] = [
		'noOfPiecesRcp',
		'grossWeight',
		'rateClass',
		'chargeableWeight',
		'rateCharge',
		'total',
		'natureAndQuantityOfGoods',
	];
	dataSource = [{ position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' }];

	public shipperInfo: ShipmentParty = {} as ShipmentParty;
	public consigneeInfo: ShipmentParty = {} as ShipmentParty;
	public currencies: string[] = [];

	readonly SLI_MODULE = Modules.SLI;
	readonly PERMISSION_SAVE = [UserPermission.CREATE, UserPermission.UPDATE].join(',');

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly notificationService: NotificationService,
		private readonly translateService: TranslateService,
		private readonly dialog: MatDialog,
		private readonly authService: AuthService,
		private readonly router: Router,
		private readonly datePipe: DatePipe
	) {
		super();
	}

	ngAfterViewInit(): void {
		this.hawbForm
			.get('total')
			?.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				const wtOrVal = this.airPortInfoComponent.airportInfoForm.get('wtOrVal')?.value;
				if (wtOrVal === DropDownType.PREPAID) {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						weightChargePrepaid: value,
					});
				} else {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						weightChargeCollect: value,
					});
				}
			});

		this.airPortInfoComponent.airportInfoForm
			.get('declaredValueForCarriage')
			?.get('numericalValue')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				const wtOrVal = this.airPortInfoComponent.airportInfoForm.get('wtOrVal')?.value;
				const val = REGX_NUMBER_2_DECIMAL.test(value ?? '') ? Number(value) : null;

				if (wtOrVal === DropDownType.PREPAID) {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						valuationChargePrepaid: val,
					});
				} else {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						valuationChargeCollect: val,
					});
				}
			});
	}

	ngOnInit(): void {
		this.sliCreateRequestService.getCurrencies().subscribe((currencies: string[]) => {
			this.currencies = ['', ...currencies];
		});

		combineLatest([
			this.hawbForm.get('chargeableWeight')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.hawbForm.get('rateCharge')!.get('numericalValue')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
		])
			.pipe(
				tap(([chargeableWeight, rateChargeNumber]) => {
					if (Number(chargeableWeight) && Number(rateChargeNumber)) {
						this.hawbForm.patchValue({
							total: Number(chargeableWeight) * Number(rateChargeNumber),
						});
					} else {
						this.hawbForm.patchValue({
							total: 0,
						});
					}
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();

		if (this.sliNumber) {
			this.sliCreateRequestService
				.getSliDetail(this.sliNumber)
				.pipe(
					tap((result: SliCreatePayload) => {
						if (result) {
							const { shipmentParty } = result;
							const shipperInfo =
								shipmentParty.find((party) => party.companyType === ShipmentPartyCompanyType.SHIPPER) ||
								({} as ShipmentParty);
							const consigneeInfo =
								shipmentParty.find((party) => party.companyType === ShipmentPartyCompanyType.CONSIGNEE) ||
								({} as ShipmentParty);

							this.shipperInfo = shipperInfo;
							this.consigneeInfo = consigneeInfo;

							this.airPortInfoComponent.airportInfoForm.patchValue({
								departureAndRequestedRouting: result.departureLocation,
								airportOfDestination: result.arrivalLocation,
								amountOfInsurance: {
									currencyUnit: result.insuredAmount?.currencyUnit ?? '',
									numericalValue: result.insuredAmount?.numericalValue?.toString() ?? 'NIL',
								},
								wtOrVal: result.weightValuationIndicator,
								declaredValueForCarriage: {
									currencyUnit: result.declaredValueForCarriage?.currencyUnit ?? '',
									numericalValue: result.declaredValueForCarriage?.numericalValue?.toString() ?? 'NCV',
								},
								declaredValueForCustoms: {
									currencyUnit: result.declaredValueForCustoms?.currencyUnit ?? '',
									numericalValue: result.declaredValueForCustoms?.numericalValue?.toString() ?? 'NVD',
								},
							});

							this.hawbForm.patchValue({
								handingInformation: result.textualHandlingInstructions ?? '',
								noOfPiecesRcp: result.pieces?.length ?? null,
								grossWeight: result.totalGrossWeight ?? null,
								natureAndQuantityOfGoods: result.goodsDescription ?? '',
							});
						}
					})
				)
				.subscribe();
			const payload = this.authService.getPayload();
			if (payload?.sub) {
				const orgId = payload.sub.split('|')[1];
				if (orgId) {
					this.orgMgmtRequestService.getOrgInfo(orgId).subscribe((result) => {
						const personInfo = result.persons?.[0];

						this.carrierAgentComponent.carrierAgentForm.patchValue({
							company: result.companyName,
							agentIataCode: result.iataCargoAgentCode,
							country: result.countryCode,
							province: result.regionCode,
							cityName: result.cityCode,
							textualPostCode: result.textualPostCode,
							address: result.locationName,
							phoneNumber: personInfo?.phoneNumber,
							email: personInfo?.emailAddress,
						});
					});
				}
			}
		}
		if (this.hawbId) {
			this.hawbSearchRequestService
				.getHawbDetail(this.hawbId)
				.pipe(
					tap((result: HawbCreateDto) => {
						if (result) {
							const { sliPartyList, partyList, sliId, orgId } = result;
							this.sliId = sliId;
							this.orgId = orgId;
							if (sliPartyList && sliPartyList.length > 0) {
								this.shipperInfo =
									sliPartyList.find((party) => party.companyType === ShipmentPartyCompanyType.SHIPPER) ||
									({} as ShipmentParty);
								this.consigneeInfo =
									sliPartyList.find((party) => party.companyType === ShipmentPartyCompanyType.CONSIGNEE) ||
									({} as ShipmentParty);
							}
							if (partyList && partyList.length > 0) {
								this.alsoNotifies = partyList.filter((it) => !it.companyType);
							}
							const carrierAgent = partyList.find((party) => party.companyType === ShipmentPartyCompanyType.CARRIER);
							if (carrierAgent) {
								const carrierInfo: any = {
									company: carrierAgent.companyName,
									agentIataCode: carrierAgent.iataCargoAgentCode,
									country: carrierAgent.countryCode,
									province: carrierAgent.regionCode,
									cityName: carrierAgent.cityCode,
									textualPostCode: carrierAgent.textualPostCode,
									address: carrierAgent.locationName,
									phoneNumber: carrierAgent.phoneNumber,
									email: carrierAgent.emailAddress,
								};

								if (this.hawbId) {
									carrierInfo.id = carrierAgent.id;
								}

								this.carrierAgentComponent.carrierAgentForm.patchValue(carrierInfo, {
									emitEvent: false,
								});
							}

							const {
								waybillPrefix,
								waybillNumber,
								accountingInformation,
								textualHandlingInstructions,
								totalGrossWeight,
								rateClassCode,
								totalVolumetricWeight,
								rateCharge,
								goodsDescriptionForRate,
								carrierDeclarationDate,
								carrierDeclarationPlace,
								consignorDeclarationSignature,
								carrierDeclarationSignature,
							} = result;
							this.hawbForm.patchValue({
								hawbPrefix: waybillPrefix,
								hawbNumber: waybillNumber,
								accountingInformation: accountingInformation as any,
								handingInformation: textualHandlingInstructions as any,
								// noOfPiecesRcp: null,
								grossWeight: totalGrossWeight as any,
								rateClass: rateClassCode,
								chargeableWeight: totalVolumetricWeight as any,
								rateCharge: {
									currencyUnit: rateCharge.currencyUnit,
									numericalValue: rateCharge.numericalValue,
								},
								natureAndQuantityOfGoods: goodsDescriptionForRate,
								date: new Date(carrierDeclarationDate),
								atPlace: carrierDeclarationPlace,
								signatureOfShipperOrHisAgent: consignorDeclarationSignature,
								signatureOfCarrierOrItsAgent: carrierDeclarationSignature,
							});

							this.airPortInfoComponent.airportInfoForm.patchValue({
								departureAndRequestedRouting: result.departureLocation,
								airportOfDestination: result.arrivalLocation,
								amountOfInsurance: {
									currencyUnit: result.insuredAmount?.currencyUnit,
									numericalValue: result.insuredAmount?.numericalValue?.toString() ?? 'NIL',
								},
								// following data will come from mawb
								// flight
								// to
								// toBy2ndCarrier
								// toBy3rdCarrier
								// date
								// byFirstCarrier
								// by2ndCarrier
								// by3rdCarrier
								wtOrVal: result.weightValuationIndicator,
								other: result.otherChargesIndicator,
								declaredValueForCarriage: {
									currencyUnit: result.declaredValueForCarriage?.currencyUnit,
									numericalValue: result.declaredValueForCarriage?.numericalValue?.toString() ?? 'NCV',
								},
								declaredValueForCustoms: {
									currencyUnit: result.declaredValueForCustoms?.currencyUnit,
									numericalValue: result.declaredValueForCustoms?.numericalValue?.toString() ?? 'NVD',
								},
							});

							const { otherChargeList } = result;
							if (otherChargeList && otherChargeList.length > 0) {
								const otherChargesList = otherChargeList.map((it) => {
									return {
										id: it.id,
										chargePaymentType: it.chargePaymentType,
										entitlement: it.entitlement,
										otherChargeCode: it.otherChargeCode,
										otherChargeAmount: {
											currencyUnit: it.otherChargeAmount?.currencyUnit ?? '',
											numericalValue: it.otherChargeAmount?.numericalValue ?? null,
										},
									};
								});

								this.otherChargesComponent.initOtherCharges(otherChargesList);
							}
						}
					})
				)
				.subscribe();
		}
	}

	onWtOrValChange(event: MatSelectChange) {
		const wtOrVal = event.value;
		const declaredValueForCarriage = this.airPortInfoComponent.airportInfoForm
			.get('declaredValueForCarriage')
			?.get('numericalValue')?.value;
		if (wtOrVal === DropDownType.PREPAID) {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				weightChargePrepaid: this.hawbForm.get('total')?.value,
				weightChargeCollect: null,
				valuationChargePrepaid: declaredValueForCarriage ? Number(declaredValueForCarriage) : null,
				valuationChargeCollect: null,
			});
		} else {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				weightChargePrepaid: null,
				weightChargeCollect: this.hawbForm.get('total')?.value,
				valuationChargePrepaid: null,
				valuationChargeCollect: declaredValueForCarriage ? Number(declaredValueForCarriage) : null,
			});
		}
	}

	onOtherChargesChange(emitCharges: EmitCharges[]) {
		const isPrepaid = emitCharges.some((item) => item.chargePaymentType === DropDownType.PREPAID);
		const isCollect = emitCharges.some((item) => item.chargePaymentType === DropDownType.COLLECT);

		if (!isPrepaid) {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
		}
		if (!isCollect) {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				taxCollect: null,
				totalOtherChargesDueAgentCollect: null,
				totalOtherChargesDueCarrierCollect: null,
			});
		}

		emitCharges.forEach((item) => {
			if (item.chargePaymentType === DropDownType.PREPAID) {
				this.prepaidCollectComponent.prepaidForm.patchValue({
					taxPrepaid: item.taxCharges,
					totalOtherChargesDueAgentPrepaid: item.agentCharges,
					totalOtherChargesDueCarrierPrepaid: item.carrierCharges,
				});
			} else {
				this.prepaidCollectComponent.prepaidForm.patchValue({
					taxCollect: item.taxCharges,
					totalOtherChargesDueAgentCollect: item.agentCharges,
					totalOtherChargesDueCarrierCollect: item.carrierCharges,
				});
			}
		});
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			textualPostCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					return {
						...item,
						companyName: result.companyName,
						contactName:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						textualPostCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
						emailAddress:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
						companyType: '',
					};
				}
				return item;
			});
		});
	}

	onCancel() {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant('common.dialog.cancel.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.isConfirmed = true;
				this.router.navigate(['/hawb/create']);
			}
		});
	}

	private markFormGroupTouched(formGroup: FormGroup) {
		Object.values(formGroup.controls).forEach((control) => {
			control.markAsTouched();
			if (control instanceof FormGroup) {
				this.markFormGroupTouched(control);
			}
		});
	}

	onSave() {
		this.hawbForm.markAllAsTouched();
		this.markFormGroupTouched(this.hawbForm.controls.rateCharge);
		this.airPortInfoComponent.airportInfoForm.markAllAsTouched();
		this.carrierAgentComponent.carrierAgentForm.markAllAsTouched();
		if (
			this.hawbForm.invalid ||
			this.carrierAgentComponent.carrierAgentForm.invalid ||
			this.airPortInfoComponent.airportInfoForm.invalid
		) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translateService.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		const {
			hawbPrefix,
			hawbNumber,
			accountingInformation,
			handingInformation,
			// noOfPiecesRcp,
			grossWeight,
			rateClass,
			chargeableWeight,
			rateCharge,
			// total,
			natureAndQuantityOfGoods,
			date,
			atPlace,
			signatureOfShipperOrHisAgent,
			signatureOfCarrierOrItsAgent,
		} = this.hawbForm.value;

		const airPortInfo = this.airPortInfoComponent.airportInfoForm.value;

		const otherCharges: OtherChargeList[] = this.otherChargesComponent.otherChargesList.map((it) => {
			return {
				...(this.hawbId && it.id ? { id: it.id } : {}),
				chargePaymentType: it.chargePaymentType,
				entitlement: it.entitlement,
				otherChargeCode: it.otherChargeCode,
				otherChargeAmount: it.otherChargeAmount,
			};
		});

		const alsoNotifies: PartyList[] = this.alsoNotifies.map((item) => {
			const itemInfo: PartyList = {
				companyName: item.companyName,
				contactName: item.contactName,
				countryCode: item.countryCode,
				regionCode: item.regionCode,
				locationName: item.locationName,
				cityCode: item.cityCode,
				textualPostCode: item.textualPostCode,
				phoneNumber: item.phoneNumber,
				emailAddress: item.emailAddress,
				companyType: item.companyType,
				iataCargoAgentCode: '',
			};

			if (this.hawbId && item.id) {
				itemInfo.id = item.id;
			}

			return itemInfo;
		});

		this.carrierAgentComponent.carrierAgentForm.markAllAsTouched();
		if (this.carrierAgentComponent.carrierAgentForm.invalid) {
			this.notificationService.showError(this.translateService.instant('hawb.carrierAgent.formInvalid'));
			return;
		}

		const carrierAgentValue = this.carrierAgentComponent.carrierAgentForm.value;

		const saveData = {
			orgId: '',
			// waybillType: '',
			waybillPrefix: hawbPrefix!,
			waybillNumber: hawbNumber!,
			partyList: [
				...alsoNotifies,
				{
					...(this.hawbId && carrierAgentValue.id ? { id: carrierAgentValue.id } : {}),
					companyName: carrierAgentValue.company ?? '',
					companyType: 'FFW',
					countryCode: carrierAgentValue.country ?? '',
					contactName: '',
					regionCode: carrierAgentValue.province ?? '',
					locationName: carrierAgentValue.address ?? '',
					iataCargoAgentCode: carrierAgentValue.agentIataCode ?? '',
					cityCode: carrierAgentValue.cityName ?? '',
					textualPostCode: carrierAgentValue.textualPostCode ?? '',
					phoneNumber: carrierAgentValue.phoneNumber ?? '',
					emailAddress: carrierAgentValue.email ?? '',
				},
			],
			accountingInformation: accountingInformation!,
			departureLocation: airPortInfo.departureAndRequestedRouting ?? '',
			arrivalLocation: airPortInfo.airportOfDestination ?? '',
			insuredAmount: {
				currencyUnit: airPortInfo.amountOfInsurance?.currencyUnit ?? '',
				numericalValue: airPortInfo.amountOfInsurance?.numericalValue
					? Number(airPortInfo.amountOfInsurance?.numericalValue)
					: null,
			},
			weightValuationIndicator: airPortInfo.wtOrVal ?? '',
			otherChargesIndicator: airPortInfo.other ?? '',
			declaredValueForCarriage: {
				currencyUnit: airPortInfo.declaredValueForCarriage?.currencyUnit ?? '',
				numericalValue: airPortInfo.declaredValueForCarriage?.numericalValue
					? Number(airPortInfo.declaredValueForCarriage?.numericalValue)
					: null,
			},
			declaredValueForCustoms: {
				currencyUnit: airPortInfo.declaredValueForCustoms?.currencyUnit ?? '',
				numericalValue: airPortInfo.declaredValueForCustoms?.numericalValue
					? Number(airPortInfo.declaredValueForCustoms?.numericalValue)
					: null,
			},
			textualHandlingInstructions: handingInformation ?? '',
			totalGrossWeight: !isBlank(grossWeight) ? Number(grossWeight) : null,
			rateClassCode: isBlank(rateClass) || rateClass === '' ? null : rateClass,
			totalVolumetricWeight: +(chargeableWeight ?? 0),
			rateCharge: {
				currencyUnit: rateCharge?.currencyUnit ?? '',
				numericalValue: rateCharge?.numericalValue ?? null,
			},
			goodsDescriptionForRate: natureAndQuantityOfGoods ?? '',
			otherChargeList: otherCharges,
			carrierDeclarationDate: this.datePipe.transform(date, DATE_FORMAT) ?? '',
			carrierDeclarationPlace: atPlace!,
			consignorDeclarationSignature: signatureOfShipperOrHisAgent!,
			carrierDeclarationSignature: signatureOfCarrierOrItsAgent!,
		};
		if (this.sliNumber && !this.hawbId) {
			this.hawbSearchRequestService
				.createHawb(this.sliNumber, saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('hawb.createHawb.success'));
						this.router.navigate(['/hawb']);
					})
				)
				.subscribe();
		}

		if (this.hawbId && this.sliId && this.orgId) {
			this.hawbSearchRequestService
				.updateHawb(this.hawbId, this.sliId, this.orgId, saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('hawb.updateHawb.success'));
						this.router.navigate(['/hawb']);
					}),
					catchError(() => {
						this.notificationService.showError(this.translateService.instant('hawb.updateHawb.error'));
						return EMPTY;
					})
				)
				.subscribe();
		}
	}
}
