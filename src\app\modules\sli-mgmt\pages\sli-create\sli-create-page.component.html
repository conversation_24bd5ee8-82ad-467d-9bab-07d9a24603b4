<div class="orll-sli-create-page">
	<div class="row">
		<div class="iata-box orll-sli-shipper__box col-6">
			<orll-sli-shipper #sliShipper [shipperInfo]="shipperInfo"></orll-sli-shipper>
		</div>
		<div class="iata-box orll-sli-consignee__box col-6">
			<orll-sli-consignee #sliConsignee title="consignee" [shipmentParty]="consigneeInfo"></orll-sli-consignee>
		</div>
	</div>

	<div class="row margin-r-5">
		<div class="iata-box orll-sli-also-notify__box col-12">
			@for (alsoNotify of alsoNotifies; track $index) {
				<mat-expansion-panel class="orll-sli-also-notify__panel" [expanded]="true">
					<mat-expansion-panel-header>
						<mat-panel-title>
							<h2 class="mat-display-2 orll-sli-also-notify__title">
								{{'sli.mgmt.company.alsoNotify' | translate}}
							</h2>
						</mat-panel-title>
						<mat-panel-description>
							<button mat-icon-button color="primary" (click)="delAlsoNotify($index, $event)" class="orll-sli-also-notify__delete-button">
								<mat-icon>delete</mat-icon>
							</button>
							<button mat-icon-button color="primary" (click)="getOrgList($index, $event)" class="orll-sli-also-notify__contact-button">
								<mat-icon>contacts</mat-icon>
							</button>
						</mat-panel-description>
					</mat-expansion-panel-header>
					<orll-sli-consignee #sliAlsoNotify [shipmentParty]="alsoNotify"></orll-sli-consignee>
				</mat-expansion-panel>
			}

			<div class="orll-sli-also-notify__footer">
				<button mat-stroked-button color="primary" type="button" (click)="addAlsoNotify()" class="orll-sli-also-notify__add-button">
					<mat-icon>add</mat-icon>
					{{'sli.mgmt.company.alsoNotify' | translate}}
				</button>
			</div>
		</div>
	</div>

	<div class="row margin-r-5">
		<div class="iata-box orll-sli-routing__box col-12">
			<orll-sli-routing #sliRouting></orll-sli-routing>
		</div>
	</div>

	<div class="row margin-r-5">
		<div class="iata-box orll-sli-routing__box col-12">
			<orll-sli-piece-list #sliPieceList [sliNumber]="sliNumber" (saveRequest)="onSave($event)"></orll-sli-piece-list>
		</div>
	</div>

	<div class="orll-sli-create-page__footer col-12">
		<button mat-stroked-button color="primary" (click)="onCancel()" class="orll-sli-create-page__cancel-button">
			{{'sli.mgmt.cancel' | translate}}
		</button>
		@if (hasPermission(PERMISSION_SAVE, SLI_MODULE) | async) {
			<button mat-flat-button color="primary" (click)="onSave()">
				<mat-icon>save</mat-icon>
				{{'sli.mgmt.save' | translate}}
			</button>
		}
	</div>

	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
