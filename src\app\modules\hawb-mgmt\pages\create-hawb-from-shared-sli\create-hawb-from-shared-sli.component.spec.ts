import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import CreateHawbFromSharedSliComponent from './create-hawb-from-shared-sli.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('CreateHawbFromSharedSliComponent', () => {
	let component: CreateHawbFromSharedSliComponent;
	let fixture: ComponentFixture<CreateHawbFromSharedSliComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [CreateHawbFromSharedSliComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(CreateHawbFromSharedSliComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
