import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import CreateHawbFromSharedSliComponent from './create-hawb-from-shared-sli.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';

describe('CreateHawbFromSharedSliComponent', () => {
	let component: CreateHawbFromSharedSliComponent;
	let fixture: ComponentFixture<CreateHawbFromSharedSliComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));
		await TestBed.configureTestingModule({
			imports: [CreateHawbFromSharedSliComponent],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
			],
		}).compileComponents();

		fixture = TestBed.createComponent(CreateHawbFromSharedSliComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
