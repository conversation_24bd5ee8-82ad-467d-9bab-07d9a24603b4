import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';

import CreateHawbFromSharedSliComponent from './create-hawb-from-shared-sli.component';
import { UserProfileService } from '@shared/services/user-profile.service';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { AuthService } from '@shared/auth/auth.service';

describe('CreateHawbFromSharedSliComponent', () => {
	let component: CreateHawbFromSharedSliComponent;
	let fixture: ComponentFixture<CreateHawbFromSharedSliComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	let hawbSearchRequestServiceSpy: jasmine.SpyObj<HawbSearchRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;
	let notificationServiceSpy: jasmine.SpyObj<NotificationService>;
	let authServiceSpy: jasmine.SpyObj<AuthService>;
	let routerSpy: jasmine.SpyObj<Router>;

	let datePipeSpy: jasmine.SpyObj<DatePipe>;

	beforeEach(async () => {
		// Create spies
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', [
			'getCurrencies',
			'getSliDetail',
			'getAirports',
			'getCountries',
			'getCurrencies',
		]);
		hawbSearchRequestServiceSpy = jasmine.createSpyObj('HawbSearchRequestService', ['getHawbDetail', 'createHawb', 'updateHawb']);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgInfo']);
		notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);

		datePipeSpy = jasmine.createSpyObj('DatePipe', ['transform']);

		// Setup default spy returns
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));
		sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of(['USD', 'EUR', 'GBP']));
		sliCreateRequestServiceSpy.getAirports.and.returnValue(
			of([
				{ code: 'LAX', name: 'Los Angeles' },
				{ code: 'JFK', name: 'New York' },
				{ code: 'LHR', name: 'London' },
			] as any)
		);
		sliCreateRequestServiceSpy.getCountries.and.returnValue(
			of([
				{ code: 'US', name: 'United States' },
				{ code: 'UK', name: 'United Kingdom' },
				{ code: 'CA', name: 'Canada' },
			] as any)
		);
		sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
			of({
				shipmentParty: [],
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
				textualHandlingInstructions: 'Handle with care',
				pieces: [],
				totalGrossWeight: 100,
			} as any)
		);
		hawbSearchRequestServiceSpy.getHawbDetail.and.returnValue(
			of({
				shipmentParty: [],
				sliPartyList: [],
				partyList: [],
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
				waybillPrefix: 'AWB',
				waybillNumber: '12345',
				sliId: 'test-sli-id',
				orgId: 'test-org-id',
			} as any)
		);
		hawbSearchRequestServiceSpy.createHawb.and.returnValue(of('success'));
		hawbSearchRequestServiceSpy.updateHawb.and.returnValue(of('success'));
		orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(
			of({
				id: 'test-org',
				companyName: 'Test Organization',
				partyRole: 'AIRLINE',
				countryCode: 'US',
				locationName: '123 Test St',
				regionCode: 'CA',
				textualPostCode: '90210',
				cityCode: 'LA',
				persons: [{ phoneNumber: '************', emailAddress: '<EMAIL>' }],
				iataCargoAgentCode: 'TEST123',
			} as any)
		);

		datePipeSpy.transform.and.returnValue('2023-01-01 10:00:00');

		await TestBed.configureTestingModule({
			imports: [
				CreateHawbFromSharedSliComponent,
				ReactiveFormsModule,
				MatDialogModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
				MatButtonModule,
				MatDatepickerModule,
				MatNativeDateModule,
				BrowserAnimationsModule,
				TranslateModule.forRoot(),
			],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: UserProfileService, useValue: userProfileServiceSpy },
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: HawbSearchRequestService, useValue: hawbSearchRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
				{ provide: NotificationService, useValue: notificationServiceSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: DatePipe, useValue: datePipeSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(CreateHawbFromSharedSliComponent);
		component = fixture.componentInstance;

		// Mock child components with proper FormGroup instances to prevent runtime errors
		const mockCarrierAgentForm = new FormGroup({
			carrierCode: new FormControl(''),
			carrierName: new FormControl(''),
			company: new FormControl(''),
			agentIataCode: new FormControl(''),
			country: new FormControl(''),
		});
		spyOn(mockCarrierAgentForm, 'patchValue').and.callThrough();

		const mockAirportInfoForm = new FormGroup({
			origin: new FormControl(''),
			destination: new FormControl(''),
			departureAndRequestedRouting: new FormControl(''),
			airportOfDestination: new FormControl(''),
			amountOfInsurance: new FormControl(''),
			wtOrVal: new FormControl(''),
		});
		spyOn(mockAirportInfoForm, 'patchValue').and.callThrough();
		spyOn(mockAirportInfoForm, 'get').and.returnValue(new FormControl('PREPAID'));

		const mockOtherChargesForm = new FormGroup({
			charges: new FormControl(''),
		});
		spyOn(mockOtherChargesForm, 'patchValue').and.callThrough();

		const mockPrepaidForm = new FormGroup({
			prepaid: new FormControl(''),
			weightChargePrepaid: new FormControl(''),
			weightChargeCollect: new FormControl(''),
			valuationChargePrepaid: new FormControl(''),
			valuationChargeCollect: new FormControl(''),
			taxPrepaid: new FormControl(''),
			taxCollect: new FormControl(''),
		});
		spyOn(mockPrepaidForm, 'patchValue').and.callThrough();

		const mockCollectForm = new FormGroup({
			collect: new FormControl(''),
		});
		spyOn(mockCollectForm, 'patchValue').and.callThrough();

		component.carrierAgentComponent = {
			carrierAgentForm: mockCarrierAgentForm,
		} as any;
		component.airPortInfoComponent = {
			airportInfoForm: mockAirportInfoForm,
		} as any;
		component.otherChargesComponent = {
			otherChargesForm: mockOtherChargesForm,
		} as any;
		component.prepaidCollectComponent = {
			prepaidForm: mockPrepaidForm,
			collectForm: mockCollectForm,
		} as any;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form on ngOnInit', () => {
		expect(component.hawbForm).toBeDefined();
		expect(component.hawbForm.get('hawbPrefix')).toBeTruthy();
		expect(component.hawbForm.get('hawbNumber')).toBeTruthy();
		expect(component.hawbForm.get('chargeableWeight')).toBeTruthy();
		expect(component.hawbForm.get('rateCharge')).toBeTruthy();
	});

	it('should call getCurrencies on ngOnInit', () => {
		component.ngOnInit();
		expect(sliCreateRequestServiceSpy.getCurrencies).toHaveBeenCalled();
	});

	it('should handle sliNumber input', () => {
		component.sliNumber = 'SLI-123';
		component.ngOnInit();
		expect(sliCreateRequestServiceSpy.getSliDetail).toHaveBeenCalledWith('SLI-123');
	});

	it('should handle hawbId input', () => {
		component.hawbId = 'HAWB-456';
		component.ngOnInit();
		expect(hawbSearchRequestServiceSpy.getHawbDetail).toHaveBeenCalledWith('HAWB-456');
	});
});
