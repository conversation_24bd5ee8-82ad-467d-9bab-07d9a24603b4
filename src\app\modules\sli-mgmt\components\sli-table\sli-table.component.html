<div class="orll-sli-table__container">
	@if (hasPermission(createPermission, sliModule) | async) {
		<div class="orll-sli-table__create">
			<button mat-flat-button color="primary" (click)="createSli()">
				<mat-icon>add</mat-icon>
				{{'sli.mgmt.create' | translate}}
			</button>
		</div>
	}
	<table mat-table [dataSource]="dataSource" [trackBy]="trackBySliCode"
		   matSort
		   (matSortChange)="onSortChange($event)"
		   aria-label="SLI table"
		   class="orll-sli-table__mat">

		<ng-container matColumnDef="waybillNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-number-width">{{'sli.table.column.sliCode' | translate}}</th>
			<td mat-cell *matCellDef="let record">
				<a class="sli-number__link" (click)="editSli(record.sliNumber)">
					{{record.waybillNumber}}
				</a>
			</td>
		</ng-container>

		<ng-container matColumnDef="shipper">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-shipper-width">{{'sli.table.column.shipper' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.shipper}}</td>
		</ng-container>

		<ng-container matColumnDef="consignee">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-consignee-width">{{'sli.table.column.consignee' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.consignee}}</td>
		</ng-container>

		<ng-container matColumnDef="goodsDescription">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-description-width">{{'sli.table.column.goodsDescription' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.goodsDescription}}</td>
		</ng-container>

		<ng-container matColumnDef="departureLocation">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-airport-width">{{'sli.table.column.departureLocation' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.departureLocation}}</td>
		</ng-container>

		<ng-container matColumnDef="arrivalLocation">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-airport-width">{{'sli.table.column.arrivalLocation' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.arrivalLocation}}</td>
		</ng-container>

		@if (hasSomeRole(shipperRoles) | async) {
			<ng-container matColumnDef="createDate">
				<th scope="col" mat-header-cell
					*matHeaderCellDef mat-sort-header class="sli-date-width">{{'sli.table.column.createDate' | translate}}</th>
				<td mat-cell *matCellDef="let record">{{record.createDate}}</td>
			</ng-container>
		}
		@if (hasSomeRole(forwarderRoles) | async) {
			<ng-container matColumnDef="receivedFrom">
				<th scope="col" mat-header-cell
					*matHeaderCellDef mat-sort-header class="sli-date-width">{{'sli.table.column.receivedFrom' | translate}}</th>
				<td mat-cell *matCellDef="let record">{{record.receiveDate}}</td>
			</ng-container>
		}

		@if (hasSomeRole(shipperRoles) | async) {
			<ng-container matColumnDef="hawbNumber">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-number-width">{{'sli.table.column.hawbNumber' | translate}}</th>
				<td mat-cell *matCellDef="let record">{{record.hawbNumber}}</td>
			</ng-container>
		}
		@if (hasSomeRole(forwarderRoles) | async) {
			<ng-container matColumnDef="hawbNumber">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header
					class="hawb-number-width">{{ 'sli.table.column.hawbNumber' | translate }}
				</th>
				<td mat-cell *matCellDef="let record">
					<a mat-stroked-button class="create-hawb-button" [routerLink]="['/hawb/create' , record.sliNumber , 'detail']">
						{{ 'sli.mgmt.createHawb'|translate }}
					</a>
				</td>
			</ng-container>
		}

		@if (hasPermission(sharePermission, sliModule) | async) {
			<ng-container matColumnDef="share">
				<th scope="col" mat-header-cell
					*matHeaderCellDef class="sli-share-width">{{'sli.table.column.share' | translate}}</th>
				<td mat-cell *matCellDef="let record">
					<button mat-icon-button aria-label="Share a SLI record" class="share-button" (click)="shareSli.emit(record)">
						<mat-icon>share</mat-icon>
					</button>
				</td>
			</ng-container>
		}

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns;" class="orll-sli-table__row"></tr>
	</table>
</div>

<mat-paginator
   [pageSizeOptions]="tablePageSizes"
   [length]="totalRecords"
   (page)="pagination.emit($event)"
></mat-paginator>
