// eslint-disable-next-line
export const environment = {
	production: false,
	faqUrl: 'https://orll-staging.iata-asd.com',
	supportUrl: 'https://orll-staging.iata-asd.com',

	msalClientId: 'c916a67e-3f7b-4d65-994b-fa9ec0255bf7',
	msalAuthority: 'https://iataazurenonprodb2c.b2clogin.com/iataazurenonprodb2c.onmicrosoft.com/B2C_1A_signup_signin',
	msalScopeBaseUrl: 'https://iataazurenonprodb2c.onmicrosoft.com/orll-staging',
	msalAfterLoginUrl: 'https://orll-staging.iata-asd.com',
	msalAfterLogoutUrl: 'https://iata--pprod.sandbox.my.site.com/csportal/s/login/?language=en_US',

	msalEnabled: true,
	backendEnabled: true,
	baseApi: 'https://orll-staging.iata-asd.com/api',
	mfBaseUrl: 'https://orll-staging.iata-asd.com/assets',

	userId: 'shipper1',
	orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943',

	userList: [
		{
			userId: 'shipper1',
			orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943',
		},
		{
			userId: 'forwarder1',
			orgId: '00dfa338-41a0-4d3f-9aab-ef309cf453de',
		},
	],
};
