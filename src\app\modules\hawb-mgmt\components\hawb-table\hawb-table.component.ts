import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorIntl, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { AsyncPipe } from '@angular/common';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';

@Component({
	selector: 'orll-hawb-table',
	templateUrl: './hawb-table.component.html',
	styleUrls: ['./hawb-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatButtonModule, MatMenuModule, MatIconModule, MatPaginatorModule, TranslateModule, AsyncPipe],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class HawbTableComponent extends RolesAwareComponent implements OnChanges {
	@Input() records: HawbListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;

	@Output() shareHawb: EventEmitter<HawbListObject> = new EventEmitter<HawbListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	readonly displayedColumns: string[] = [
		'hawbNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'origin',
		'destination',
		'createDate',
		'mawbNumber',
		'share',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<HawbListObject>(this.records || []);

	readonly HAWB_MODULE = Modules.HAWB;
	readonly PERMISSION_CREATE = UserPermission.CREATE;
	readonly PERMISSION_SHARE = UserPermission.SHARE;

	constructor(private readonly router: Router) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	onSortChange(event: Sort): void {
		this.currentSort = event;
		this.sortChange.emit(event);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createHawbFromSli(): void {
		this.router.navigate(['/hawb/create']);
	}

	editHawb(hawbId: string): void {
		this.router.navigate(['/hawb/edit', hawbId]);
	}

	// eslint-disable-next-line
	trackByHawbId(_index: number, record: HawbListObject): string {
		return record.hawbId + record.createDate;
	}
}
