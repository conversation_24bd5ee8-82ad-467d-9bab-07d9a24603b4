import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@environments/environment';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { Observable, of } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

export class UserProfileServiceMock {
	getProfile(): Observable<UserProfile> {
		return of({} as UserProfile);
	}
}

describe('UserProfileService', () => {
	let service: UserProfileService;
	let httpController: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [],
			providers: [UserProfileService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(UserProfileService);
		httpController = TestBed.inject(HttpTestingController);
	});

	it('#getProfile should retrieve the user profile', (done: DoneFn) => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service.getProfile().subscribe((user: UserProfile) => {
			expect(user).toEqual(userStub);
			expect(service['profile']).toEqual(userStub);
			done();
		});

		const req = httpController.expectOne({
			method: 'GET',
			url: `${environment.baseApi}/user/user/current`,
		});

		req.flush(userStub);
	});

	it('#getProfile might return the cached user profile', (done: DoneFn) => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service['profile'] = userStub;

		service.getProfile().subscribe((user: UserProfile) => {
			expect(user).toEqual(userStub);
			done();
		});
	});

	it('#currentUser should return the cached user profile', () => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service['profile'] = userStub;
		const user = service.currentUser;
		expect(user).toEqual(userStub);
	});

	it('#currentUser might return null if user profile was not retrieved yet', () => {
		expect(service.currentUser).toBeNull();
	});
});
