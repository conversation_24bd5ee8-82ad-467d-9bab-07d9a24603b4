import { Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';
import { UserProfileService } from './user-profile.service';

@Injectable({ providedIn: 'root' })
export class RouteGuardService {
	constructor(private readonly userProfileService: UserProfileService) {}

	isSuperUser(): Observable<boolean> {
		return of(this.userProfileService.isSuperUser());
	}

	hasSomeRole(neededRoles: string[]): Observable<boolean> {
		if (!neededRoles?.length) {
			return of(true);
		}

		return this.userProfileService.hasSomeRole(neededRoles).pipe(
			map((hasRole: boolean) => {
				return hasRole;
			})
		);
	}
}
